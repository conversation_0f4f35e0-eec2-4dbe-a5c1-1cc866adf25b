package com.Liyichong.VR;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;

import com.hwb.timecontroller.service.ICheckService;

public class CheckServiceClient {
    private static final String TAG = "CheckServiceClient";
    private static final String SERVICE_PACKAGE = "com.hwb.timecontroller";
    private static final String SERVICE_CLASS = "com.hwb.timecontroller.service.CheckService";

    private Context context;
    private ICheckService checkService;
    private boolean isConnected = false;
    private CheckServiceListener listener;

    public interface CheckServiceListener {
        void onServiceConnected();

        void onServiceDisconnected();
    }

    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "CheckService连接成功");
            checkService = ICheckService.Stub.asInterface(service);
            isConnected = true;

            if (listener != null) {
                listener.onServiceConnected();
            }
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "CheckService连接断开");
            checkService = null;
            isConnected = false;

            if (listener != null) {
                listener.onServiceDisconnected();
            }
        }
    };

    public CheckServiceClient(Context context) {
        this.context = context.getApplicationContext();
    }

    public void setListener(CheckServiceListener listener) {
        this.listener = listener;
    }

    /**
     * 连接到CheckService
     */
    public boolean connect() {
        try {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(SERVICE_PACKAGE, SERVICE_CLASS));

            boolean result = context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
            Log.d(TAG, "绑定CheckService结果: " + result);
            return result;
        } catch (Exception e) {
            Log.e(TAG, "连接CheckService失败", e);
            return false;
        }
    }

    /**
     * 断开CheckService连接
     */
    public void disconnect() {
        try {
            if (isConnected) {
                context.unbindService(serviceConnection);
            }
        } catch (Exception e) {
            Log.e(TAG, "解绑服务失败", e);
        }

        isConnected = false;
        checkService = null;
    }

    /**
     * 检查权限
     * @param needShowLock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    public boolean check(boolean needShowLock) {
        if (!isConnected || checkService == null) {
            Log.w(TAG, "服务未连接，无法检查权限");
            return false;
        }

        try {
            boolean result = checkService.check(needShowLock);
            Log.d(TAG, "检查权限: needShowLock=" + needShowLock + " -> " + result);
            return result;
        } catch (RemoteException e) {
            Log.e(TAG, "检查权限失败", e);
            return false;
        }
    }


    /**
     * 跳转到指定页面
     * @param pageType 页面类型。1. 用户信息页面（未登录时会跳到登录页面） 2. 设置页面（自动校验密码）
     * @return true-跳转成功, false-跳转失败
     */
    public boolean jumpToPage(int pageType) {
        if (!isConnected || checkService == null) {
            Log.w(TAG, "服务未连接，无法检查权限");
            return false;
        }

        try {
            boolean result = checkService.jumpToPage(pageType);
            Log.d(TAG, "跳转页面: pageType=" + pageType + " -> " + result);
            return result;
        } catch (RemoteException e) {
            Log.e(TAG, "检查权限失败", e);
            return false;
        }
    }


    public boolean isConnected() {
        return isConnected;
    }
}
