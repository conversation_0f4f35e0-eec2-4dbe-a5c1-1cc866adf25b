<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.Liyichong.VR" >

<!--    <queries>-->
<!--        &lt;!&ndash; 声明要访问的AIDL服务包 &ndash;&gt;-->
<!--        <package android:name="com.hwb.timecontroller"/>-->
<!--        &lt;!&ndash; 声明要查询所有响应 MAIN+LAUNCHER 的 Activity &ndash;&gt;-->
<!--        <intent>-->
<!--            <action android:name="android.intent.action.MAIN"/>-->
<!--            <category android:name="android.intent.category.LAUNCHER"/>-->
<!--        </intent>-->
<!--    </queries>-->

    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
    <application
        android:allowBackup="true"
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:supportsRtl="true">
        <activity
            android:name=".Install"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <meta-data android:name="unityplayer.UnityActivity" android:value="true"/>
        </activity>
        <service
            android:name=".OnlineService"
            android:enabled="true"
            android:exported="true" />
        <!-- 注释掉错误的Google Actions配置，应该使用FileProvider的配置 -->
        <!--
        <meta-data
            android:name="com.google.android.actions"
            android:resource="@xml/provider_paths" />
        -->
        <!-- 适配android 7.0以及以上更新APK路径 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
    </application>

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.REPLACE_EXISTING_PACKAGE"/>
</manifest>
