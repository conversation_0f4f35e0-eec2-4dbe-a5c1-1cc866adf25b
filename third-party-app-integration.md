# 第三方应用集成ICheckService指南

## 概述

本文档提供第三方应用如何集成TimeController的ICheckService进行应用启动权限验证的完整指南。支持Android原生应用和Unity应用集成。

## ICheckService接口说明

### AIDL接口定义

```java
package com.hwb.timecontroller.service;

interface ICheckService {
    /**
     * 检查应用启动权限
     * @param need_show_lock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    boolean check(boolean need_show_lock);
}
```

### 服务信息

- **包名**: `com.hwb.timecontroller`
- **服务类**: `com.hwb.timecontroller.service.CheckService`
- **Action**: `com.hwb.timecontroller.service.ICheckService`

### 方法说明

#### `check(boolean need_show_lock)`

**功能**: 检查应用启动权限

**参数**:
- `need_show_lock`: 是否需要显示锁定界面
  - `true`: 如果验证失败，会自动启动LockActivity让用户登录验证
  - `false`: 仅返回验证结果，不显示界面

**返回值**:
- `true`: 允许启动应用（倒计时正在运行）
- `false`: 不允许启动应用（需要用户验证）

**验证逻辑**:
1. 检查倒计时状态，如果正在运行则返回`true`
2. 如果`need_show_lock=true`且验证失败，启动LockActivity
3. 返回验证结果

## Android原生应用集成

### 1. 配置build.gradle

在您的Android项目的`app/build.gradle`文件中启用AIDL支持：

```gradle
android {

    defaultConfig {
        // 其他配置...
    }

    buildFeatures {
        aidl true  // 启用AIDL支持
    }

    // 其他配置...
}
```

### 2. 添加AIDL文件

在您的Android项目中创建AIDL文件：

**文件路径**: `app/src/main/aidl/com/hwb/timecontroller/service/ICheckService.aidl`

```java
package com.hwb.timecontroller.service;

interface ICheckService {
    boolean check(boolean need_show_lock);
}
```

**注意**:
- AIDL文件的包名必须与TimeController项目保持一致
- 文件路径结构必须与包名对应
- 添加AIDL文件后需要重新构建项目以生成对应的Java接口类

### 3. Java集成代码

```java
package com.yourcompany.yourapp;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.os.IBinder;
import android.os.RemoteException;
import android.util.Log;
import com.hwb.timecontroller.service.ICheckService;

public class CheckServiceClient {
    private static final String TAG = "CheckServiceClient";
    private static final String SERVICE_PACKAGE = "com.hwb.timecontroller";
    private static final String SERVICE_CLASS = "com.hwb.timecontroller.service.CheckService";
    
    private Context context;
    private ICheckService checkService;
    private boolean isConnected = false;
    private CheckServiceListener listener;
    
    public interface CheckServiceListener {
        void onServiceConnected();
        void onServiceDisconnected();
    }
    
    private ServiceConnection serviceConnection = new ServiceConnection() {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
            Log.d(TAG, "CheckService连接成功");
            checkService = ICheckService.Stub.asInterface(service);
            isConnected = true;
            
            if (listener != null) {
                listener.onServiceConnected();
            }
        }
        
        @Override
        public void onServiceDisconnected(ComponentName name) {
            Log.d(TAG, "CheckService连接断开");
            checkService = null;
            isConnected = false;
            
            if (listener != null) {
                listener.onServiceDisconnected();
            }
        }
    };
    
    public CheckServiceClient(Context context) {
        this.context = context.getApplicationContext();
    }
    
    public void setListener(CheckServiceListener listener) {
        this.listener = listener;
    }
    
    /**
     * 连接到CheckService
     */
    public boolean connect() {
        try {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(SERVICE_PACKAGE, SERVICE_CLASS));
            
            boolean result = context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE);
            Log.d(TAG, "绑定CheckService结果: " + result);
            return result;
        } catch (Exception e) {
            Log.e(TAG, "连接CheckService失败", e);
            return false;
        }
    }
    
    /**
     * 断开CheckService连接
     */
    public void disconnect() {
        try {
            if (isConnected) {
                context.unbindService(serviceConnection);
            }
        } catch (Exception e) {
            Log.e(TAG, "解绑服务失败", e);
        }
        
        isConnected = false;
        checkService = null;
    }
    
    /**
     * 检查权限
     * @param needShowLock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    public boolean check(boolean needShowLock) {
        if (!isConnected || checkService == null) {
            Log.w(TAG, "服务未连接，无法检查权限");
            return false;
        }
        
        try {
            boolean result = checkService.check(needShowLock);
            Log.d(TAG, "检查权限: needShowLock=" + needShowLock + " -> " + result);
            return result;
        } catch (RemoteException e) {
            Log.e(TAG, "检查权限失败", e);
            return false;
        }
    }
    
    public boolean isConnected() {
        return isConnected;
    }
}
```

### 4. Kotlin集成代码

```kotlin
package com.yourcompany.yourapp

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import com.hwb.timecontroller.service.ICheckService

class CheckServiceClient(context: Context) {
    companion object {
        private const val TAG = "CheckServiceClient"
        private const val SERVICE_PACKAGE = "com.hwb.timecontroller"
        private const val SERVICE_CLASS = "com.hwb.timecontroller.service.CheckService"
    }
    
    interface CheckServiceListener {
        fun onServiceConnected()
        fun onServiceDisconnected()
    }
    
    private val context = context.applicationContext
    private var checkService: ICheckService? = null
    private var isConnected = false
    private var listener: CheckServiceListener? = null
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            Log.d(TAG, "CheckService连接成功")
            checkService = ICheckService.Stub.asInterface(service)
            isConnected = true
            listener?.onServiceConnected()
        }
        
        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(TAG, "CheckService连接断开")
            checkService = null
            isConnected = false
            listener?.onServiceDisconnected()
        }
    }
    
    fun setListener(listener: CheckServiceListener) {
        this.listener = listener
    }
    
    /**
     * 连接到CheckService
     */
    fun connect(): Boolean {
        return try {
            val intent = Intent().apply {
                component = ComponentName(SERVICE_PACKAGE, SERVICE_CLASS)
            }
            
            val result = context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
            Log.d(TAG, "绑定CheckService结果: $result")
            result
        } catch (e: Exception) {
            Log.e(TAG, "连接CheckService失败", e)
            false
        }
    }
    
    /**
     * 断开CheckService连接
     */
    fun disconnect() {
        try {
            if (isConnected) {
                context.unbindService(serviceConnection)
            }
        } catch (e: Exception) {
            Log.e(TAG, "解绑服务失败", e)
        }
        
        isConnected = false
        checkService = null
    }
    
    /**
     * 检查权限
     * @param needShowLock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    fun check(needShowLock: Boolean): Boolean {
        if (!isConnected || checkService == null) {
            Log.w(TAG, "服务未连接，无法检查权限")
            return false
        }
        
        return try {
            val result = checkService!!.check(needShowLock)
            Log.d(TAG, "检查权限: needShowLock=$needShowLock -> $result")
            result
        } catch (e: RemoteException) {
            Log.e(TAG, "检查权限失败", e)
            false
        }
    }
    
    fun isConnected(): Boolean = isConnected
}
```

### 5. 使用示例

```java
public class MainActivity extends AppCompatActivity {
    private CheckServiceClient checkServiceClient;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        // 初始化CheckService客户端
        checkServiceClient = new CheckServiceClient(this);
        checkServiceClient.setListener(new CheckServiceClient.CheckServiceListener() {
            @Override
            public void onServiceConnected() {
                Log.d("MainActivity", "CheckService连接成功，可以开始使用");
            }
            
            @Override
            public void onServiceDisconnected() {
                Log.d("MainActivity", "CheckService连接断开");
            }
        });
        
        // 连接服务
        checkServiceClient.connect();
    }
    
    private void launchApp(String packageName) {
        // 检查权限，需要显示锁定界面
        boolean canLaunch = checkServiceClient.check(true);
        
        if (canLaunch) {
            Log.d("MainActivity", "允许启动应用: " + packageName);
            // 启动应用的逻辑
            startAppByPackageName(packageName);
        } else {
            Log.d("MainActivity", "需要用户验证，CheckService会自动启动LockActivity");
            // CheckService会自动启动LockActivity进行用户验证
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (checkServiceClient != null) {
            checkServiceClient.disconnect();
        }
    }
}
```

## Unity应用集成

### 1. Unity C#代码

```csharp
using UnityEngine;

public class CheckServiceManager : MonoBehaviour
{
    private AndroidJavaObject checkServiceClient;
    private AndroidJavaObject unityActivity;
    
    void Start()
    {
        InitializeCheckService();
    }
    
    void InitializeCheckService()
    {
        if (Application.platform == RuntimePlatform.Android)
        {
            // 获取Unity Activity
            AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            unityActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
            
            // 创建CheckServiceClient实例
            checkServiceClient = new AndroidJavaObject("com.yourcompany.yourapp.CheckServiceClient", unityActivity);
            
            // 连接服务
            bool connected = checkServiceClient.Call<bool>("connect");
            Debug.Log($"CheckService连接结果: {connected}");
        }
    }
    
    /// <summary>
    /// 检查权限
    /// </summary>
    /// <param name="needShowLock">是否需要显示锁定界面进行验证</param>
    /// <returns>true-允许启动, false-不允许启动</returns>
    public bool Check(bool needShowLock)
    {
        if (checkServiceClient != null)
        {
            return checkServiceClient.Call<bool>("check", needShowLock);
        }
        return false;
    }
    
    void OnDestroy()
    {
        if (checkServiceClient != null)
        {
            checkServiceClient.Call("disconnect");
        }
    }
}
```

### 2. Unity使用示例

```csharp
public class AppLauncher : MonoBehaviour
{
    private CheckServiceManager checkServiceManager;
    
    void Start()
    {
        checkServiceManager = FindObjectOfType<CheckServiceManager>();
    }
    
    public void LaunchApp(string packageName)
    {
        // 检查权限，需要显示锁定界面
        bool canLaunch = checkServiceManager.Check(true);
        
        if (canLaunch)
        {
            Debug.Log($"允许启动应用: {packageName}");
            LaunchAndroidApp(packageName);
        }
        else
        {
            Debug.Log("需要用户验证，CheckService会自动启动LockActivity");
        }
    }
    
    private void LaunchAndroidApp(string packageName)
    {
        AndroidJavaClass unityPlayer = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
        AndroidJavaObject currentActivity = unityPlayer.GetStatic<AndroidJavaObject>("currentActivity");
        AndroidJavaObject packageManager = currentActivity.Call<AndroidJavaObject>("getPackageManager");
        AndroidJavaObject launchIntent = packageManager.Call<AndroidJavaObject>("getLaunchIntentForPackage", packageName);
        
        if (launchIntent != null)
        {
            currentActivity.Call("startActivity", launchIntent);
        }
    }
}
```



## 权限配置

### AndroidManifest.xml配置

所有第三方应用都需要在AndroidManifest.xml中添加以下配置：

```xml
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 声明要查询的包（Android 11+必需） -->
    <queries>
        <package android:name="com.hwb.timecontroller" />
    </queries>

    <application>
        <!-- 应用配置 -->
    </application>
</manifest>
```

**重要说明**：
- `<queries>`标签对于Android 11 (API 30)+设备是必需的
- 没有`<queries>`声明，应用将无法发现和绑定到TimeController的CheckService
- `<queries>`标签必须放在`<application>`标签外面
- **不需要**`QUERY_ALL_PACKAGES`权限，`<queries>`标签已足够

## 最佳实践

### 1. 连接管理

```java
public class CheckServiceManager {
    private CheckServiceClient client;
    private boolean isInitialized = false;

    public void initialize(Context context) {
        if (!isInitialized) {
            client = new CheckServiceClient(context);
            client.setListener(new CheckServiceClient.CheckServiceListener() {
                @Override
                public void onServiceConnected() {
                    Log.d("CheckServiceManager", "服务连接成功");
                }

                @Override
                public void onServiceDisconnected() {
                    Log.d("CheckServiceManager", "服务连接断开，尝试重连");
                    // 实现重连逻辑
                    reconnect();
                }
            });
            isInitialized = true;
        }
    }

    private void reconnect() {
        // 延迟重连，避免频繁重连
        new Handler().postDelayed(() -> {
            if (client != null) {
                client.connect();
            }
        }, 3000);
    }
}
```

### 2. 错误处理

```java
public boolean checkWithRetry(boolean needShowLock, int maxRetries) {
    for (int i = 0; i < maxRetries; i++) {
        try {
            if (client.isConnected()) {
                return client.check(needShowLock);
            } else {
                // 尝试重新连接
                if (client.connect()) {
                    // 等待连接建立
                    Thread.sleep(1000);
                    if (client.isConnected()) {
                        return client.check(needShowLock);
                    }
                }
            }
        } catch (Exception e) {
            Log.e("CheckService", "检查权限失败，重试 " + (i + 1) + "/" + maxRetries, e);
        }

        // 重试间隔
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
        }
    }

    return false; // 所有重试都失败
}
```

### 3. 生命周期管理

```java
public class BaseActivity extends AppCompatActivity {
    protected CheckServiceClient checkServiceClient;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        checkServiceClient = new CheckServiceClient(this);
        checkServiceClient.connect();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 确保服务连接
        if (!checkServiceClient.isConnected()) {
            checkServiceClient.connect();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (checkServiceClient != null) {
            checkServiceClient.disconnect();
        }
    }
}
```

## 故障排除

### 常见问题

1. **服务连接失败**
   - 检查TimeController应用是否已安装并运行
   - 确认CheckService是否已启动
   - 检查包名和服务类名是否正确

2. **权限不足**
   - 确保应用已获得QUERY_ALL_PACKAGES权限
   - 检查目标SDK版本和权限配置

3. **AIDL编译错误**
   - 确保AIDL文件路径正确：`app/src/main/aidl/com/hwb/timecontroller/service/ICheckService.aidl`
   - 检查包名是否与TimeController一致
   - 清理并重新构建项目

4. **调用返回false**
   - 检查TimeController的倒计时状态
   - 确认应用是否在白名单中
   - 查看TimeController的日志输出

### 调试方法

```java
// 启用详细日志
public void enableDebugLogging() {
    checkServiceClient.setListener(new CheckServiceClient.CheckServiceListener() {
        @Override
        public void onServiceConnected() {
            Log.d("DEBUG", "CheckService连接成功");

            // 测试调用
            boolean result = checkServiceClient.check(false);
            Log.d("DEBUG", "测试调用结果: " + result);
        }

        @Override
        public void onServiceDisconnected() {
            Log.d("DEBUG", "CheckService连接断开");
        }
    });
}
```

### 检查TimeController状态

```java
// 检查TimeController是否安装
public boolean isTimeControllerInstalled(Context context) {
    try {
        PackageManager pm = context.getPackageManager();
        pm.getPackageInfo("com.hwb.timecontroller", PackageManager.GET_ACTIVITIES);
        return true;
    } catch (PackageManager.NameNotFoundException e) {
        return false;
    }
}

// 检查CheckService是否运行
public boolean isCheckServiceRunning(Context context) {
    ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
    for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
        if ("com.hwb.timecontroller.service.CheckService".equals(service.service.getClassName())) {
            return true;
        }
    }
    return false;
}
```