package com.Liyichong.VR;

import android.app.ActivityManager;
import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.media.AudioAttributes;
import android.media.AudioFocusRequest;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.IBinder;
import android.util.Log;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

public class OnlineService extends Service {
    private int CHANNEL_ONE_ID = 0x1982;
    private int count = 0;

    private int HVRVendorId = 4817;
    private int HVRProductId = 4242;
    private int HVRProductId2 = 4222;

    //音乐播放
    private MediaPlayer mMediaPlayer;
    private AudioManager mAudioManager;
    private AudioAttributes mPlaybackAttributes;
    private AudioFocusRequest mFocusRequest;
    private Handler handler = new Handler();
    private final String TAG = "LogMusicPlay";

    private UsbManager usbManager;

    @Override
    public IBinder onBind(Intent intent) {
        return null;
        //throw new UnsupportedOperationException("Not yet implemented");
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 已屏蔽USB监听功能
        // StartWatchUSB();
        //StartPlaySound();
        //startForeground(CHANNEL_ONE_ID, getNotification("VR场景接入", "点击后请戴上眼镜体验VR场景"));
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        //return super.onStartCommand(intent, flags, startId);
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        // 已屏蔽USB监听功能
        // getApplicationContext().unregisterReceiver(usbroadcastreceiver);
        //stopPlayMusic();
        if (AppUtils.isDebug()){
            Toast.makeText(getApplicationContext(), "OnlineService 停止", Toast.LENGTH_SHORT).show();
        }
        super.onDestroy();
    }

    public Intent LaunchSelf(Context context) {
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (intent != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        return intent;
    }

    public Intent LaunchVR(Context context){
        //PackageManager packageManager = context.getPackageManager();
        //Intent intent = packageManager.getLaunchIntentForPackage("com.Liyichong.VRGame");

        Intent intent = new Intent();
        intent.setPackage("com.Liyichong.VRGame");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction("com.huawei.android.vr.PROMPT");
        return intent;
    }

    private static final String ACTION_USB_PERMISSION = "com.android.example.USB_PERMISSION";
    public void StartWatchUSB() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_DETACHED);
        filter.addAction(ACTION_USB_PERMISSION);
        filter.addAction("android.hardware.usb.action.USB_STATE");
        filter.addAction("com.android.vrservice.glass");
        filter.addAction("com.huawei.vrservice.permission.VR");
        getApplicationContext().registerReceiver(usbroadcastreceiver, filter);
    }

    //获取USB设备列表
    public List<UsbDevice> getDeviceList() {
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        Iterator<UsbDevice> deviceIterator = deviceList.values().iterator();
        List<UsbDevice> usbDevices = new ArrayList<>();
        while (deviceIterator.hasNext()) {
            UsbDevice device = deviceIterator.next();
            usbDevices.add(device);
            Log.e("USBUtil", "getDeviceList: " + device.getDeviceName());
        }
        return  usbDevices;
    }
    //获取指定设备
    public UsbDevice getUSBDevice(int vendorId, int productId) {
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        Iterator<UsbDevice> deviceIterator = deviceList.values().iterator();
        List<UsbDevice> usbDevices = new ArrayList<>();
        while (deviceIterator.hasNext()) {
            UsbDevice device = deviceIterator.next();
            if(device.getVendorId() == vendorId && device.getProductId() == productId){
                return  device;
            }
        }
        return null;
    }

    public boolean hasPermission(UsbDevice device){
        return usbManager.hasPermission(device);
    }

    BroadcastReceiver usbroadcastreceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Toast.makeText(context, "action =" + action, Toast.LENGTH_LONG).show();
            if(UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                //boolean connected = intent.getExtras().getBoolean("connected");
                //Toast.makeText(context, "有USB插入1 =" + connected + connected, Toast.LENGTH_LONG).show();
                //有新设备插入，这里一般要判断这个设备是不是我们想要的，是的话请求权限
                usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
                UsbDevice device = getUSBDevice(HVRVendorId, HVRProductId);
                if (device != null) {
                    //Toast.makeText(context, "有USB插入1 =" + device.getDeviceName(), Toast.LENGTH_LONG).show();
                    if (isBackground(context)) {
                        startActivity(LaunchSelf(context));
                    } else {
                        startActivity(LaunchVR(context));
                    }
                }else{
                    Toast.makeText(context, "没有对应的设备。", Toast.LENGTH_SHORT).show();
                }
            } else if(UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                //有设备拔出了
                Log.e("=======deviceInfo", "DETACHED");
                //startActivity(LaunchSelf(context));
    //            startForeground(CHANNEL_ONE_ID, getNotification("VR场景接入", "点击后请戴上眼镜体验VR场景"));
            }
            else if (action.equalsIgnoreCase("android.hardware.usb.action.USB_STATE")) {
                Log.e("=======deviceInfo", "USB_STATE");
    /*                Toast.makeText(context, "有USB动作 = USB_STATE", Toast.LENGTH_LONG).show();

                usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
                UsbDevice device = getUSBDevice(HVRVendorId, HVRProductId);
                if (device != null) {
                    Toast.makeText(context, "有USB插入2 =" + device.getDeviceName(), Toast.LENGTH_LONG).show();
                    if (isBackground(context)) {
                        startActivity(LaunchSelf(context));
                    } else {
                        startActivity(LaunchVR(context));
                    }
                }else{
                    Toast.makeText(context, "没有对应的设备。", Toast.LENGTH_SHORT).show();
                }
    */

                //if(IsHVRDevice(intent, context)) {
                //取不到设备  不判断if(IsHVRDevice

                //if (count >= 1) {
                //    if (isBackground(context)) {
                        //startActivity(LaunchSelf(context));
                        //DelayedCall(new Runnable() {
                        //    @Override
                        //    public void run() {
                        //        startActivity(LaunchVR(context));
                        //    }
                        //}, 500);
                //   } else {
                //        startActivity(LaunchVR(context));
                //    }
                //}
            }
        }
    };

    private void DelayedCall(Runnable runnable, long time){
        new Handler().postDelayed(runnable, time);
    }

    private boolean IsHVRDevice(final Intent intent, Context context){
        boolean isHVRDevice = false;
        final UsbDevice device = intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
        Log.e("=======deviceInfo", "IsHVRDevice: " + (device!=null));
        Toast.makeText(context, "===========IsHVRDevice: " + (device!=null), Toast.LENGTH_LONG).show();
        if(device!=null) {
            int vendorId = device.getVendorId();
            int productId = device.getProductId();

            Log.e("=======deviceInfo", vendorId + " " + productId);
            //isHVRDevice = (vendorId == HVRVendorId && (productId == HVRProductId || productId == HVRProductId2));
            isHVRDevice = true;
        }
        return isHVRDevice;
    }

    /**
     * 判断当前应用是否处于前台
     */
    public static boolean isBackground(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(context.getPackageName())) {
                if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                    System.out.print(String.format("Foreground App:", appProcess.processName));
                    return false;
                }else{
                    System.out.print("Background App:"+appProcess.processName);
                    return true;
                }
            }
        }
        return false;
    }

///////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////PlaySound////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////////////////
    /*
    public void StartPlaySound(){
        //音标处理
        mAudioManager = (AudioManager) getSystemService(AUDIO_SERVICE);
        if (mAudioManager != null) {
            //mAudioManager.requestAudioFocus(mAudioFocusChange, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN);
            mPlaybackAttributes = new AudioAttributes.Builder()
                    .setUsage(AudioAttributes.USAGE_MEDIA)
                    .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                    .build();
            mFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                    .setAudioAttributes(mPlaybackAttributes)
                    .setAcceptsDelayedFocusGain(true)
                    .setWillPauseWhenDucked(true)
                    .setOnAudioFocusChangeListener(mAudioFocusChange, handler)
                    .build();
            mAudioManager.requestAudioFocus(mFocusRequest);
        }
        mMediaPlayer = MediaPlayer.create(getApplicationContext(), 0);//R.raw.no_sound_music
        mMediaPlayer.setLooping(true);

        startPlayMusic();
    }

    private AudioManager.OnAudioFocusChangeListener mAudioFocusChange = new
            AudioManager.OnAudioFocusChangeListener() {
                @Override
                public void onAudioFocusChange(int focusChange) {
                    switch (focusChange) {
                        case AudioManager.AUDIOFOCUS_GAIN:
                            Log.e(TAG, "AUDIOFOCUS_GAIN");
                            try {
                                startPlayMusic();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            break;
                        case AudioManager.AUDIOFOCUS_LOSS:
                            Log.e(TAG, "AUDIOFOCUS_LOSS");

                            //pausePlayMusic();
                            try {
                                startPlayMusic();
                            } catch (Exception e) {
                                e.printStackTrace();
                            }

                            releaseAudioFocus();
                            break;
                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT:
                            Log.e(TAG, "AUDIOFOCUS_LOSS_TRANSIENT");
                            pausePlayMusic();
                            break;
                        case AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK:
                            Log.e(TAG, "AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK");
                            // lower the volume
                            break;
                    }
                }
            };

    private void startPlayMusic() {
        if (mMediaPlayer != null && !mMediaPlayer.isPlaying()) {
            Log.e(TAG, "启动后台播放音乐");
            mMediaPlayer.start();
        }
    }

    private void pausePlayMusic(){
        if (mMediaPlayer != null) {
            mMediaPlayer.pause();
        }
    }

    private void stopPlayMusic() {
        if (mMediaPlayer != null) {
            Log.e(TAG, "关闭后台播放音乐");
            mMediaPlayer.stop();
        }
    }

    / * *
     * 释放音频焦点
     * /
    public void releaseAudioFocus() {
        //mAudioManager.unregisterMediaButtonEventReceiver(RemoteControlReceiver);
        //版本兼容
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            mAudioManager.abandonAudioFocusRequest(mFocusRequest);
        } else {
            mAudioManager.abandonAudioFocus(mAudioFocusChange);
        }

        // Wait 30 seconds before stopping playback
        //handler.postDelayed(delayedStopRunnable, TimeUnit.SECONDS.toMillis(30))
    }

    ///////////////////////////////////////////////////////////////////////////////////////////
    //////////////////////////////////Notification/////////////////////////////////////////////
    ///////////////////////////////////////////////////////////////////////////////////////////
    private Notification getNotification(String title, String message){
        count++;
        // 创建一个用于页面跳转的延迟意图 CreateVRIntents替换clickIntent
        PendingIntent contentIntent = PendingIntent.getActivity(this, count, LaunchVR(this),
                PendingIntent.FLAG_UPDATE_CURRENT);
        // 创建一个通知消息的构造器
        Notification.Builder builder = new Notification.Builder(this, "channel_id");
        if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
            String CHANNEL_ONE_NAME = title;
            NotificationChannel notificationChannel = new NotificationChannel("" + CHANNEL_ONE_ID, CHANNEL_ONE_NAME, NotificationManager.IMPORTANCE_MIN);
            notificationChannel.enableLights(false);
            notificationChannel.setShowBadge(false);//是否显示角标
            notificationChannel.setLockscreenVisibility(Notification.VISIBILITY_SECRET);
            NotificationManager systemService = (NotificationManager) getSystemService(NOTIFICATION_SERVICE);
            systemService.createNotificationChannel(notificationChannel);
            builder.setChannelId("" + CHANNEL_ONE_ID);
        }
        builder.setContentIntent(contentIntent) // 设置内容的点击意图
                .setAutoCancel(false) // 设置是否允许自动清除
                .setSmallIcon(R.mipmap.ic_launcher) // 设置状态栏里的小图标
                .setTicker("提示消息来啦") // 设置状态栏里的提示文本
                .setWhen(System.currentTimeMillis()) // 设置推送时间，格式为 小时:分
                .setLargeIcon(BitmapFactory.decodeResource(getResources(), R.mipmap.ic_launcher)) // 设置通知栏里的大图标
                .setContentTitle(title) // 设置通知栏里的标题文本
                .setContentText(message)// 设置通知栏力度内容文本
                .setOngoing(true); //设置他为一个正在进行的通知。他们通常是用来表示一个后台任务,用户积极参与(如播放音乐)或以某种方式正在等待,因此占用设备(如一个文件下载,同步操作,主动网络连接)

        //根据消息构造器创建一个通知对象
        //if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN){
        Notification notify = builder.build();
        notify.flags |= Notification.FLAG_NO_CLEAR;
        notify.flags |= Notification.FLAG_ONGOING_EVENT;
        return notify;
        //}
    }*/
}