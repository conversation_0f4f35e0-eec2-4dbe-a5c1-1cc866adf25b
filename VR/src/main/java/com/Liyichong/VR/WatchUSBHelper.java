package com.Liyichong.VR;

import android.app.ActivityManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.util.Log;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

/**
 * 监听辅助
 * <p>
 * Author:huangwubin
 * <p>
 * changeLogs:
 * 2022/9/24: First created this class.
 */
public class WatchUSBHelper {
    private static final String ACTION_USB_PERMISSION = "com.android.example.USB_PERMISSION";
    private int HVRVendorId = 4817;
    private int HVRProductId = 4242;
    private int HVRProductId2 = 4222;

    private Context context;
    private UsbManager usbManager;

    private final BroadcastReceiver usbroadcastreceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Toast.makeText(context, "action =" + action, Toast.LENGTH_LONG).show();
            if (UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(action)) {
                if (AppUtils.isDebug()){
                    toast("收到设备插入监听");
                }
                //boolean connected = intent.getExtras().getBoolean("connected");
                //Toast.makeText(context, "有USB插入1 =" + connected + connected, Toast.LENGTH_LONG).show();
                //有新设备插入，这里一般要判断这个设备是不是我们想要的，是的话请求权限
                usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
                UsbDevice device = getUSBDevice(HVRVendorId, HVRProductId);
                if (device != null) {
                    //Toast.makeText(context, "有USB插入1 =" + device.getDeviceName(), Toast.LENGTH_LONG).show();
                    if (isBackground(context)) {
                        context.startActivity(LaunchSelf(context));
                    } else {
                        context.startActivity(LaunchVR(context));
                    }
                } else {
                    Toast.makeText(context, "没有对应的设备。", Toast.LENGTH_SHORT).show();
                }
            } else if (UsbManager.ACTION_USB_DEVICE_DETACHED.equals(action)) {
                //有设备拔出了
                if (AppUtils.isDebug()){
                    toast("收到设备拔出监听");
                }
                Log.e("=======deviceInfo", "DETACHED");
                //startActivity(LaunchSelf(context));
                //            startForeground(CHANNEL_ONE_ID, getNotification("VR场景接入", "点击后请戴上眼镜体验VR场景"));
            } else if (action.equalsIgnoreCase("android.hardware.usb.action.USB_STATE")) {
                Log.e("=======deviceInfo", "USB_STATE");
                toast("收到监听：USB_STATE");
    /*                Toast.makeText(context, "有USB动作 = USB_STATE", Toast.LENGTH_LONG).show();

                usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
                UsbDevice device = getUSBDevice(HVRVendorId, HVRProductId);
                if (device != null) {
                    Toast.makeText(context, "有USB插入2 =" + device.getDeviceName(), Toast.LENGTH_LONG).show();
                    if (isBackground(context)) {
                        startActivity(LaunchSelf(context));
                    } else {
                        startActivity(LaunchVR(context));
                    }
                }else{
                    Toast.makeText(context, "没有对应的设备。", Toast.LENGTH_SHORT).show();
                }
    */

                //if(IsHVRDevice(intent, context)) {
                //取不到设备  不判断if(IsHVRDevice

                //if (count >= 1) {
                //    if (isBackground(context)) {
                //startActivity(LaunchSelf(context));
                //DelayedCall(new Runnable() {
                //    @Override
                //    public void run() {
                //        startActivity(LaunchVR(context));
                //    }
                //}, 500);
                //   } else {
                //        startActivity(LaunchVR(context));
                //    }
                //}
            }
        }
    };

    public WatchUSBHelper(Context context) {
        this.context = context;
    }

    public void startWatchUSB() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_ATTACHED);
        filter.addAction(UsbManager.ACTION_USB_ACCESSORY_DETACHED);
        filter.addAction(ACTION_USB_PERMISSION);
        filter.addAction("android.hardware.usb.action.USB_STATE");
        filter.addAction("com.android.vrservice.glass");
        filter.addAction("com.huawei.vrservice.permission.VR");
        context.registerReceiver(usbroadcastreceiver, filter);
    }

    /**
     * 在Activity 的onDestroy 调用
     */
    public void onDestroy() {
        context.unregisterReceiver(usbroadcastreceiver);
    }


    //获取指定设备
    private UsbDevice getUSBDevice(int vendorId, int productId) {
        HashMap<String, UsbDevice> deviceList = usbManager.getDeviceList();
        Iterator<UsbDevice> deviceIterator = deviceList.values().iterator();
        List<UsbDevice> usbDevices = new ArrayList<>();
        while (deviceIterator.hasNext()) {
            UsbDevice device = deviceIterator.next();
            if (device.getVendorId() == vendorId && device.getProductId() == productId) {
                return device;
            }
        }
        return null;
    }

    /**
     * 判断当前应用是否处于前台
     */
    private boolean isBackground(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.processName.equals(context.getPackageName())) {
                if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                    System.out.print(String.format("Foreground App:", appProcess.processName));
                    return false;
                } else {
                    System.out.print("Background App:" + appProcess.processName);
                    return true;
                }
            }
        }
        return false;
    }


    private Intent LaunchSelf(Context context) {
        Intent intent = context.getPackageManager().getLaunchIntentForPackage(context.getPackageName());
        if (intent != null) {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        return intent;
    }

    private Intent LaunchVR(Context context) {
        //PackageManager packageManager = context.getPackageManager();
        //Intent intent = packageManager.getLaunchIntentForPackage("com.Liyichong.VRGame");

        Intent intent = new Intent();
        intent.setPackage("com.Liyichong.VRGame");
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setAction("com.huawei.android.vr.PROMPT");
        return intent;
    }

    private void toast(String msg){
        Toast.makeText(context, msg, Toast.LENGTH_SHORT).show();
    }
}
