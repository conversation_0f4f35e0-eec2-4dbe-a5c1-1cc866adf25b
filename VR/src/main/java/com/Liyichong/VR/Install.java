package com.Liyichong.VR;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import com.unity3d.player.UnityPlayer;
import com.unity3d.player.UnityPlayerActivity;

import java.io.File;

public class Install extends UnityPlayerActivity {

    private static final String TAG = "Install";
    // private WatchUSBHelper watchUSBHelper; // 已屏蔽USB监听功能
    private CheckServiceClient checkServiceClient;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // setContentView(R.layout.activity_main);

//        Context context = getApplicationContext();
//
//        Intent intent = new Intent(context, OnlineService.class);
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            context.startForegroundService(intent);
//        } else {
//            context.startService(intent);
//        }

        AppUtils.syncIsDebug(this);

        // 已屏蔽USB监听功能
        // watchUSBHelper = new WatchUSBHelper(getApplicationContext());
        // watchUSBHelper.startWatchUSB();

        // 初始化CheckService客户端
        initCheckService();
    }

    @Override
    protected void onDestroy() {
        // 已屏蔽USB监听功能
        // watchUSBHelper.onDestroy();

        // 断开CheckService连接
        if (checkServiceClient != null) {
            checkServiceClient.disconnect();
        }

        super.onDestroy();
    }

    public void InstallApkUnity(String apkPath) {
        UnityPlayer.UnitySendMessage("_gameController", "CallUnity", apkPath);
        //这一行的目的是调用unity的方法，可以不要
        File file = new File(apkPath);
        Intent intent = new Intent(Intent.ACTION_VIEW);
        if (Build.VERSION.SDK_INT >= 24) { //Android 7.0及以上
            intent.setFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            Uri apkUri = FileProvider.getUriForFile(getApplicationContext(), UnityPlayer.currentActivity.getPackageName() + ".fileprovider", file);//记住修改包名
            //对目标应用临时授权该Uri所代表的文件
            intent.setDataAndType(apkUri, "application/vnd.android.package-archive");
        } else {
            intent.setDataAndType(Uri.fromFile(file), "application/vnd.android.package-archive");
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        }
        this.startActivity(intent);
    }

    //显示提示信息
    public void ShowToast(final String msg) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Toast.makeText(getApplicationContext(), msg, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 初始化CheckService客户端
     */
    private void initCheckService() {
        checkServiceClient = new CheckServiceClient(this);
        checkServiceClient.setListener(new CheckServiceClient.CheckServiceListener() {
            @Override
            public void onServiceConnected() {
                Log.d(TAG, "CheckService连接成功，可以开始使用");
                ShowToast("权限验证服务连接成功");
            }

            @Override
            public void onServiceDisconnected() {
                Log.d(TAG, "CheckService连接断开");
                ShowToast("权限验证服务连接断开");
            }
        });

        // 连接服务
        boolean connected = checkServiceClient.connect();
        Log.d(TAG, "CheckService连接结果: " + connected);
    }

    /**
     * 检查权限 - 供Unity调用
     * @param needShowLock 是否需要显示锁定界面进行验证
     * @return true-允许启动, false-不允许启动
     */
    public boolean CheckPermission(boolean needShowLock) {
        if (checkServiceClient != null && checkServiceClient.isConnected()) {
            boolean result = checkServiceClient.check(needShowLock);
            Log.d(TAG, "权限检查结果: needShowLock=" + needShowLock + " -> " + result);
            return result;
        } else {
            Log.w(TAG, "CheckService未连接，无法检查权限");
            ShowToast("权限验证服务未连接");
            return false;
        }
    }

    /**
     * 检查权限并启动应用 - 供Unity调用
     * @param packageName 要启动的应用包名
     * @param needShowLock 是否需要显示锁定界面进行验证
     */
    public void CheckPermissionAndLaunchApp(String packageName, boolean needShowLock) {
        boolean canLaunch = CheckPermission(needShowLock);

        if (canLaunch) {
            Log.d(TAG, "允许启动应用: " + packageName);
            ShowToast("权限验证通过，启动应用: " + packageName);
            // 这里可以添加启动应用的逻辑
            LaunchApp(packageName);
        } else {
            Log.d(TAG, "需要用户验证，CheckService会自动启动LockActivity");
            ShowToast("需要用户验证");
            // CheckService会自动启动LockActivity进行用户验证
        }
    }

    /**
     * 启动指定包名的应用
     * @param packageName 应用包名
     */
    private void LaunchApp(String packageName) {
        try {
            Intent intent = getPackageManager().getLaunchIntentForPackage(packageName);
            if (intent != null) {
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } else {
                ShowToast("未找到应用: " + packageName);
            }
        } catch (Exception e) {
            Log.e(TAG, "启动应用失败: " + packageName, e);
            ShowToast("启动应用失败: " + e.getMessage());
        }
    }
}
