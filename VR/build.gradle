apply plugin: 'com.android.library'

android {
    compileSdkVersion 31
    buildToolsVersion "31.0.0"

    defaultConfig {
        minSdkVersion 16
        //noinspection ExpiredTargetSdkVersion
        targetSdkVersion 31
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
}

dependencies {

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.1.1'
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
    compileOnly files('libs\\classes.jar')
    implementation 'androidx.appcompat:appcompat:1.0.2'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'
}

//解决aar包名和unity 主项目包名相同报错的问题
afterEvaluate {
    generateReleaseBuildConfig.enabled = false
    generateDebugBuildConfig.enabled = false
}